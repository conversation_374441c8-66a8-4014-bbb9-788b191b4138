#!/usr/bin/env python3
"""
DÉMO EXTRACTEUR DE CONTRATS BANCAIRES AVANCÉ
Version de démonstration qui fonctionne sans clés API
"""

import os
import json
import pytesseract
from pathlib import Path
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Any, Optional

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Dossiers
PDF_FOLDER = 'contrat'
IMG_FOLDER = 'images'
TEXT_FOLDER = 'ocr_texts'
RESULT_FOLDER = 'results'

class DemoContractExtractor:
    """Extracteur de démonstration avec patterns avancés"""
    
    def __init__(self):
        # Configuration OCR optimisée
        self.ocr_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ .,;:!?()[]{}/-+*=@#$%&'
        self.ocr_languages = ['fra', 'eng', 'ara']
        self.ocr_dpi = 400

    def enhance_image_advanced(self, image: Image.Image) -> Image.Image:
        """Amélioration avancée de l'image pour OCR optimal"""
        # Conversion en array numpy
        img_array = np.array(image)
        
        # Conversion en niveaux de gris si nécessaire
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array
        
        # Débruitage avancé
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Amélioration du contraste avec CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Morphologie pour nettoyer
        kernel = np.ones((1,1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # Retour en PIL Image
        return Image.fromarray(cleaned)

    def extract_text_with_advanced_ocr(self, pdf_path: str, pdf_name: str) -> str:
        """Extraction de texte avec OCR avancé multi-méthodes"""
        logger.info(f"📄 Extraction OCR avancée pour {pdf_name}")
        
        # Créer les dossiers
        os.makedirs(IMG_FOLDER, exist_ok=True)
        os.makedirs(TEXT_FOLDER, exist_ok=True)
        
        # Conversion PDF en images haute résolution
        pages = convert_from_path(pdf_path, dpi=self.ocr_dpi, fmt='PNG')
        
        all_text = ""
        
        for i, page in enumerate(pages):
            page_num = i + 1
            logger.info(f"🔍 Traitement avancé page {page_num}...")
            
            # Amélioration avancée de l'image
            enhanced_page = self.enhance_image_advanced(page)
            
            # Sauvegarde de l'image améliorée
            img_path = f"{IMG_FOLDER}/{pdf_name}_enhanced_page_{page_num}.png"
            enhanced_page.save(img_path, "PNG")
            
            # OCR avec configuration optimisée multi-langues
            ocr_results = []
            for lang in self.ocr_languages:
                try:
                    text = pytesseract.image_to_string(
                        enhanced_page, lang=lang, config=self.ocr_config
                    )
                    if text.strip():
                        ocr_results.append(text)
                except Exception as e:
                    logger.warning(f"⚠️ OCR {lang} échoué: {e}")
            
            # Combinaison des résultats OCR
            combined_text = "\n".join(ocr_results) if ocr_results else ""
            
            # Sauvegarde du texte
            text_path = f"{TEXT_FOLDER}/{pdf_name}_advanced_page_{page_num}.txt"
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(combined_text)
            
            all_text += f"\n--- Page {page_num} ---\n{combined_text}"
            
            logger.info(f"✅ Page {page_num} traitée avec OCR avancé")
        
        # Nettoyage et normalisation du texte
        cleaned_text = self.clean_and_normalize_text(all_text)
        
        return cleaned_text

    def clean_and_normalize_text(self, text: str) -> str:
        """Nettoyage et normalisation avancée du texte"""
        if not text:
            return ""
        
        # Corrections de caractères mal reconnus
        corrections = {
            'ô': 'o', 'â': 'a', 'ê': 'e', 'î': 'i', 'û': 'u',
            '0': 'O', '1': 'I', '5': 'S', '8': 'B',
            'rn': 'm', 'vv': 'w', 'ii': 'u'
        }
        
        for wrong, correct in corrections.items():
            text = text.replace(wrong, correct)
        
        # Normalisation des espaces
        text = re.sub(r'\s+', ' ', text)
        
        # Suppression des caractères de contrôle
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        
        return text.strip()

    def extract_with_advanced_patterns(self, text: str) -> Dict:
        """Extraction avec patterns ultra-avancés"""
        logger.info("🧠 Extraction avec patterns avancés...")
        
        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_financieres": {},
            "champs_masques": []
        }
        
        # Patterns ultra-précis pour informations bancaires
        bank_patterns = {
            "nom_banque": [
                r'BANQUE\s+TUNISO[\-\s]?KOWEITIENNE',
                r'BTK\b',
                r'Banque\s+Tuniso[\-\s]?Koweitienne'
            ],
            "capital_social": [
                r'capital\s+(?:social\s+)?de\s+([0-9\s\.]+)\s*(?:dinars?|DT)',
                r'au\s+capital\s+de\s+([0-9\s\.]+)',
                r'([0-9]{3}[\s\.][0-9]{3}[\s\.][0-9]{3})\s*(?:dinars?|DT)'
            ],
            "numero_rc": [
                r'R\.?C\.?\s*[=:]\s*(B[0-9]+)',
                r'RC\s*[=:]\s*(B[0-9]+)',
                r'Registre.*?Commerce.*?(B[0-9]{9,12})'
            ],
            "numero_tva": [
                r'T\.?V\.?A\.?\s*[=:]\s*([0-9]+)',
                r'TVA\s*[=:]\s*([0-9]+)'
            ],
            "adresse": [
                r'([0-9]+\s+bis,?\s+Av\.?\s+Mohamed\s+V[^|]{10,50})',
                r'(Avenue\s+Mohamed\s+V.*?Tunis)',
                r'([0-9]+\s+bis.*?Tunis)'
            ],
            "telephone": [
                r'Tél\s*:?\s*\(\+216\)\s*([0-9\s\.]+)',
                r'\(\+216\)\s*([0-9\s\.]+)'
            ]
        }
        
        # Patterns ultra-précis pour informations contrat
        contract_patterns = {
            "type_contrat": [
                r'CONTRAT\s+DE\s+(PRET|PRÊT|CREDIT|CRÉDIT)',
                r'Convention\s+de\s+([A-Za-z\s]+)',
                r'CONTRAT\s+([A-Z\s]+)'
            ],
            "numero_contrat": [
                r'(?:Numéro|N°|Ref|Référence).*?(?:contrat|convention)\s*[=:]\s*([A-Z0-9\-\/]+)',
                r'Contrat\s+(?:n°|numéro)\s*[=:]\s*([A-Z0-9\-\/]+)',
                r'\b([A-Z]{2,4}[0-9]{6,})\b'
            ],
            "date_edition": [
                r'(?:Edité|Établi)\s*le\s*[=:]?\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})',
                r'Date.*?édition\s*[=:]\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})'
            ],
            "date_signature": [
                r'(?:Fait|Signé)\s+(?:à.*?)?le\s+([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})',
                r'Date.*?signature\s*[=:]\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})'
            ]
        }
        
        # Patterns ultra-précis pour informations client
        client_patterns = {
            "code_client": [
                r'Code\s+Client\s*[=:]?\s*([A-Z0-9]{4,12})',
                r'Client\s+(?:n°|numéro)\s*[=:]?\s*([A-Z0-9]{4,12})'
            ],
            "nom": [
                r'Nom\s+(?:du\s+)?Client\s*[=:]?\s*([A-Z]{2,25})',
                r'Nom\s*[=:]?\s*([A-Z]{2,25})(?=\s+Prénom|\s+$)'
            ],
            "prenom": [
                r'Prénom\s+(?:du\s+)?Client\s*[=:]?\s*([A-Z]{2,25})',
                r'Prénom\s*[=:]?\s*([A-Z]{2,25})'
            ],
            "numero_cin": [
                r'(?:CIN|C\.I\.N)\s*[=:]?\s*([0-9]{8})',
                r'Carte.*?Identité.*?(?:n°|numéro)\s*[=:]?\s*([0-9]{8})',
                r'Pièce.*?identité.*?([0-9]{8})'
            ],
            "date_naissance": [
                r'(?:Date.*?)?[Nn]aissance\s*[=:]?\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})',
                r'Né(?:e)?\s+le\s+([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})'
            ],
            "nationalite": [
                r'Nationalité\s*[=:]?\s*([A-Z]{2,15})',
                r'Nat\.\s*([A-Z]{2,15})'
            ],
            "telephone_mobile": [
                r'(?:Mobile|Tél\.?\s+Mobile|GSM)\s*[=:]?\s*([0-9\s\.\-\+]{8,15})',
                r'(?:\+216\s*)?([0-9]{2}\s*[0-9]{3}\s*[0-9]{3})'
            ],
            "profession": [
                r'Profession\s*[=:]?\s*([A-Za-z\s]{3,30})',
                r'Activité\s*[=:]?\s*([A-Za-z\s]{3,30})'
            ]
        }
        
        # Patterns pour informations financières
        financial_patterns = {
            "montant_principal": [
                r'(?:Montant|Somme).*?(?:principal|demandé|accordé)\s*[=:]?\s*([0-9\s,\.]+)\s*(?:DT|dinars?)',
                r'Prêt\s+de\s+([0-9\s,\.]+)\s*(?:DT|dinars?)'
            ],
            "taux_interet": [
                r'Taux.*?(?:intérêt|d\'intérêt)\s*[=:]?\s*([0-9,\.]+)\s*%',
                r'([0-9,\.]+)\s*%.*?(?:an|annuel)'
            ],
            "mensualite": [
                r'(?:Mensualité|Échéance)\s*[=:]?\s*([0-9\s,\.]+)\s*(?:DT|dinars?)',
                r'Remboursement.*?mensuel\s*[=:]?\s*([0-9\s,\.]+)'
            ],
            "duree_contrat": [
                r'Durée\s*[=:]?\s*([0-9]+)\s*(?:mois|ans?)',
                r'(?:sur|pendant)\s+([0-9]+)\s*(?:mois|ans?)'
            ]
        }
        
        # Extraction avec validation
        all_patterns = {
            "informations_banque": bank_patterns,
            "informations_contrat": contract_patterns,
            "informations_client": client_patterns,
            "informations_financieres": financial_patterns
        }
        
        for section, patterns in all_patterns.items():
            for field, field_patterns in patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                    if match:
                        value = match.group(1).strip() if match.groups() else match.group(0).strip()
                        if self.validate_extracted_value(value, field):
                            result[section][field] = value
                            logger.info(f"✅ {field}: {value}")
                            break
        
        # Détection des champs masqués
        masked_patterns = [
            (r'Code\s+Client\s+(X+)', 'code_client'),
            (r'Nom.*?Client\s+(X+)', 'nom'),
            (r'Prénom.*?Client\s+(X+)', 'prenom'),
            (r'(?:CIN|Carte.*?Identité).*?(X+)', 'numero_cin'),
            (r'(?:Date.*?)?[Nn]aissance.*?(X+)', 'date_naissance'),
            (r'(?:Mobile|Téléphone).*?(X+)', 'telephone')
        ]
        
        for pattern, field_name in masked_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                result["champs_masques"].append({
                    "champ": field_name,
                    "masque": match,
                    "longueur_masque": len(match)
                })
        
        return result
