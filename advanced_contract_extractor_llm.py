#!/usr/bin/env python3
"""
EXTRACTEUR DE CONTRATS BANCAIRES AVANCÉ AVEC LLM
Utilise OpenAI GPT-4 et DeepSeek pour une précision parfaite
"""

import os
import json
import pytesseract
from pathlib import Path
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
import logging
import requests
from typing import Dict, List, Any, Optional
import openai
from openai import OpenAI
from config import get_config, validate_config

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Chargement de la configuration
CONFIG = get_config()

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = CONFIG["ocr"]["tesseract_cmd"]

# Dossiers
PDF_FOLDER = CONFIG["folders"]["pdf_folder"]
IMG_FOLDER = CONFIG["folders"]["img_folder"]
TEXT_FOLDER = CONFIG["folders"]["text_folder"]
RESULT_FOLDER = CONFIG["folders"]["result_folder"]

# Configuration des modèles LLM
OPENAI_API_KEY = CONFIG["llm"]["openai"]["api_key"]
DEEPSEEK_API_KEY = CONFIG["llm"]["deepseek"]["api_key"]
DEEPSEEK_BASE_URL = CONFIG["llm"]["deepseek"]["base_url"]

class AdvancedLLMContractExtractor:
    """Extracteur avancé avec modèles LLM pour précision parfaite"""
    
    def __init__(self):
        # Configuration OCR optimisée depuis config
        self.ocr_config = CONFIG["ocr"]["config"]
        self.ocr_languages = CONFIG["ocr"]["languages"]
        self.ocr_dpi = CONFIG["ocr"]["dpi"]

        # Initialisation des clients LLM avec gestion d'erreurs
        self.openai_client = None
        self.deepseek_client = None

        try:
            if OPENAI_API_KEY != "your-openai-api-key-here":
                self.openai_client = OpenAI(api_key=OPENAI_API_KEY)
                logger.info("✅ Client OpenAI initialisé")
        except Exception as e:
            logger.warning(f"⚠️ Erreur initialisation OpenAI: {e}")

        try:
            if DEEPSEEK_API_KEY != "your-deepseek-api-key-here":
                self.deepseek_client = OpenAI(
                    api_key=DEEPSEEK_API_KEY,
                    base_url=DEEPSEEK_BASE_URL
                )
                logger.info("✅ Client DeepSeek initialisé")
        except Exception as e:
            logger.warning(f"⚠️ Erreur initialisation DeepSeek: {e}")

        # Validation de la configuration au démarrage
        if not validate_config():
            logger.warning("⚠️ Configuration incomplète détectée")
        
        # Prompt système pour l'extraction d'informations
        self.system_prompt = """Tu es un expert en analyse de contrats bancaires tunisiens. 
        Ton rôle est d'extraire avec une précision parfaite toutes les informations importantes des contrats.
        
        INFORMATIONS À EXTRAIRE:
        
        1. INFORMATIONS BANQUE:
        - nom_banque (ex: Banque Tuniso-Koweitienne, BTK)
        - capital_social (montant en dinars)
        - numero_rc (numéro registre commerce, format B + chiffres)
        - numero_tva (numéro TVA)
        - adresse_siege (adresse complète)
        - telephone (numéro avec indicatif +216)
        - fax (numéro fax)
        
        2. INFORMATIONS CONTRAT:
        - type_contrat (ex: CONTRAT DE PRET, Convention de crédit)
        - numero_contrat (référence unique du contrat)
        - date_edition (date d'édition du contrat)
        - date_signature (date de signature)
        - montant_principal (montant du prêt/crédit)
        - taux_interet (taux d'intérêt appliqué)
        - duree_contrat (durée en mois/années)
        - garanties (garanties demandées)
        
        3. INFORMATIONS CLIENT:
        - code_client (code unique client)
        - nom (nom de famille)
        - prenom (prénom)
        - date_naissance (date de naissance)
        - lieu_naissance (lieu de naissance)
        - nationalite (nationalité)
        - situation_familiale (célibataire, marié, etc.)
        - profession (profession exercée)
        - adresse (adresse complète)
        - telephone_fixe (téléphone fixe)
        - telephone_mobile (téléphone mobile)
        - numero_cin (numéro carte d'identité)
        - date_delivrance_cin (date délivrance CIN)
        - lieu_delivrance_cin (lieu délivrance CIN)
        - revenus_mensuels (revenus déclarés)
        
        4. INFORMATIONS FINANCIÈRES:
        - montant_demande (montant demandé)
        - montant_accorde (montant accordé)
        - mensualite (montant mensualité)
        - frais_dossier (frais de dossier)
        - assurance (montant assurance)
        - total_a_rembourser (total à rembourser)
        
        RÈGLES IMPORTANTES:
        - Extrais UNIQUEMENT les informations présentes dans le texte
        - Si une information est masquée par des X, indique "MASQUÉ"
        - Respecte les formats de dates (DD/MM/YYYY)
        - Pour les montants, conserve les unités (dinars, DT, etc.)
        - Sois très précis sur les numéros (CIN, contrat, RC, etc.)
        - Si tu n'es pas sûr d'une information, indique "NON_DETECTE"
        
        Réponds UNIQUEMENT en format JSON valide."""

    def enhance_image_advanced(self, image: Image.Image) -> Image.Image:
        """Amélioration avancée de l'image pour OCR optimal"""
        # Conversion en array numpy
        img_array = np.array(image)
        
        # Conversion en niveaux de gris si nécessaire
        if len(img_array.shape) == 3:
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
        else:
            gray = img_array
        
        # Débruitage avancé
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Amélioration du contraste avec CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation adaptative
        binary = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Morphologie pour nettoyer
        kernel = np.ones((1,1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # Retour en PIL Image
        return Image.fromarray(cleaned)

    def extract_text_with_advanced_ocr(self, pdf_path: str, pdf_name: str) -> str:
        """Extraction de texte avec OCR avancé multi-méthodes"""
        logger.info(f"📄 Extraction OCR avancée pour {pdf_name}")
        
        # Créer les dossiers
        os.makedirs(IMG_FOLDER, exist_ok=True)
        os.makedirs(TEXT_FOLDER, exist_ok=True)
        
        # Conversion PDF en images haute résolution
        pages = convert_from_path(pdf_path, dpi=self.ocr_dpi, fmt=CONFIG["ocr"]["image_format"])
        
        all_text = ""
        
        for i, page in enumerate(pages):
            page_num = i + 1
            logger.info(f"🔍 Traitement avancé page {page_num}...")
            
            # Amélioration avancée de l'image
            enhanced_page = self.enhance_image_advanced(page)
            
            # Sauvegarde de l'image améliorée
            img_path = f"{IMG_FOLDER}/{pdf_name}_enhanced_page_{page_num}.png"
            enhanced_page.save(img_path, "PNG")
            
            # OCR avec configuration optimisée multi-langues
            ocr_results = []
            for lang in self.ocr_languages:
                try:
                    text = pytesseract.image_to_string(
                        enhanced_page, lang=lang, config=self.ocr_config
                    )
                    if text.strip():
                        ocr_results.append(text)
                except Exception as e:
                    logger.warning(f"⚠️ OCR {lang} échoué: {e}")

            # Combinaison des résultats OCR
            combined_text = "\n".join(ocr_results) if ocr_results else ""
            
            # Sauvegarde du texte
            text_path = f"{TEXT_FOLDER}/{pdf_name}_advanced_page_{page_num}.txt"
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(combined_text)
            
            all_text += f"\n--- Page {page_num} ---\n{combined_text}"
            
            logger.info(f"✅ Page {page_num} traitée avec OCR avancé")
        
        # Nettoyage et normalisation du texte
        cleaned_text = self.clean_and_normalize_text(all_text)
        
        return cleaned_text

    def clean_and_normalize_text(self, text: str) -> str:
        """Nettoyage et normalisation avancée du texte"""
        if not text:
            return ""
        
        # Corrections de caractères mal reconnus
        corrections = {
            'ô': 'o', 'â': 'a', 'ê': 'e', 'î': 'i', 'û': 'u',
            '0': 'O', '1': 'I', '5': 'S', '8': 'B',
            'rn': 'm', 'vv': 'w', 'ii': 'u'
        }
        
        for wrong, correct in corrections.items():
            text = text.replace(wrong, correct)
        
        # Normalisation des espaces
        text = re.sub(r'\s+', ' ', text)
        
        # Suppression des caractères de contrôle
        text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
        
        return text.strip()

    def analyze_with_openai_gpt4(self, text: str) -> Dict:
        """Analyse avec OpenAI GPT-4 pour extraction précise"""
        if not self.openai_client:
            logger.warning("⚠️ Client OpenAI non configuré")
            return {}

        try:
            logger.info("🧠 Analyse avec OpenAI GPT-4...")

            response = self.openai_client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": f"Analyse ce contrat bancaire et extrais toutes les informations importantes:\n\n{text}"}
                ],
                temperature=0.1,
                max_tokens=4000
            )

            result_text = response.choices[0].message.content

            # Parsing du JSON
            try:
                result = json.loads(result_text)
                logger.info("✅ Analyse OpenAI GPT-4 réussie")
                return result
            except json.JSONDecodeError:
                logger.error("❌ Erreur parsing JSON OpenAI")
                return {}

        except Exception as e:
            logger.error(f"❌ Erreur OpenAI: {str(e)}")
            return {}

    def analyze_with_deepseek(self, text: str) -> Dict:
        """Analyse avec DeepSeek pour validation croisée"""
        if not self.deepseek_client:
            logger.warning("⚠️ Client DeepSeek non configuré")
            return {}

        try:
            logger.info("🧠 Analyse avec DeepSeek...")

            response = self.deepseek_client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": f"Analyse ce contrat bancaire tunisien et extrais toutes les informations avec précision maximale:\n\n{text}"}
                ],
                temperature=0.1,
                max_tokens=4000
            )

            result_text = response.choices[0].message.content

            # Parsing du JSON
            try:
                result = json.loads(result_text)
                logger.info("✅ Analyse DeepSeek réussie")
                return result
            except json.JSONDecodeError:
                logger.error("❌ Erreur parsing JSON DeepSeek")
                return {}

        except Exception as e:
            logger.error(f"❌ Erreur DeepSeek: {str(e)}")
            return {}

    def merge_and_validate_results(self, openai_result: Dict, deepseek_result: Dict, fallback_result: Dict) -> Dict:
        """Fusion et validation des résultats des différents modèles"""
        logger.info("🔄 Fusion et validation des résultats...")

        # Structure finale
        final_result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_financieres": {},
            "champs_masques": [],
            "validation_croisee": {
                "openai_disponible": bool(openai_result),
                "deepseek_disponible": bool(deepseek_result),
                "consensus_score": 0.0
            }
        }

        # Priorité: OpenAI > DeepSeek > Fallback
        primary_source = openai_result if openai_result else (deepseek_result if deepseek_result else fallback_result)
        secondary_source = deepseek_result if openai_result else (openai_result if deepseek_result else {})

        # Fusion des sections
        sections = ["informations_banque", "informations_contrat", "informations_client", "informations_financieres"]

        for section in sections:
            if section in primary_source:
                final_result[section] = primary_source[section].copy()

                # Validation croisée si disponible
                if section in secondary_source:
                    self.cross_validate_section(final_result[section], secondary_source[section], section)

        # Gestion des champs masqués
        if "champs_masques" in primary_source:
            final_result["champs_masques"] = primary_source["champs_masques"]
        elif "champs_masques" in fallback_result:
            final_result["champs_masques"] = fallback_result["champs_masques"]

        # Calcul du score de consensus
        if openai_result and deepseek_result:
            final_result["validation_croisee"]["consensus_score"] = self.calculate_consensus_score(openai_result, deepseek_result)

        return final_result

    def cross_validate_section(self, primary_data: Dict, secondary_data: Dict, section_name: str):
        """Validation croisée d'une section"""
        for key, primary_value in primary_data.items():
            if key in secondary_data:
                secondary_value = secondary_data[key]

                # Si les valeurs diffèrent, on garde la primaire mais on note la différence
                if primary_value != secondary_value and secondary_value not in ["NON_DETECTE", "MASQUÉ"]:
                    primary_data[f"{key}_validation"] = {
                        "valeur_primaire": primary_value,
                        "valeur_secondaire": secondary_value,
                        "confiance": "MOYENNE" if len(str(primary_value)) > len(str(secondary_value)) else "FAIBLE"
                    }

    def calculate_consensus_score(self, result1: Dict, result2: Dict) -> float:
        """Calcule un score de consensus entre deux résultats"""
        total_fields = 0
        matching_fields = 0

        sections = ["informations_banque", "informations_contrat", "informations_client", "informations_financieres"]

        for section in sections:
            if section in result1 and section in result2:
                for key in set(list(result1[section].keys()) + list(result2[section].keys())):
                    total_fields += 1
                    if (key in result1[section] and key in result2[section] and
                        result1[section][key] == result2[section][key]):
                        matching_fields += 1

        return matching_fields / total_fields if total_fields > 0 else 0.0

    def extract_with_fallback_regex(self, text: str) -> Dict:
        """Extraction de secours avec regex améliorés"""
        logger.info("🔧 Extraction de secours avec regex avancés...")

        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "informations_financieres": {},
            "champs_masques": []
        }

        # Patterns ultra-précis pour informations bancaires
        bank_patterns = {
            "nom_banque": [
                r'BANQUE\s+TUNISO[\-\s]?KOWEITIENNE',
                r'BTK\b',
                r'Banque\s+Tuniso[\-\s]?Koweitienne'
            ],
            "capital_social": [
                r'capital\s+(?:social\s+)?de\s+([0-9\s\.]+)\s*(?:dinars?|DT)',
                r'au\s+capital\s+de\s+([0-9\s\.]+)',
                r'([0-9]{3}[\s\.][0-9]{3}[\s\.][0-9]{3})\s*(?:dinars?|DT)'
            ],
            "numero_rc": [
                r'R\.?C\.?\s*[=:]\s*(B[0-9]+)',
                r'RC\s*[=:]\s*(B[0-9]+)',
                r'Registre.*?Commerce.*?(B[0-9]{9,12})'
            ],
            "numero_tva": [
                r'T\.?V\.?A\.?\s*[=:]\s*([0-9]+)',
                r'TVA\s*[=:]\s*([0-9]+)'
            ]
        }

        # Patterns ultra-précis pour informations contrat
        contract_patterns = {
            "type_contrat": [
                r'CONTRAT\s+DE\s+(PRET|PRÊT|CREDIT|CRÉDIT)',
                r'Convention\s+de\s+([A-Za-z\s]+)',
                r'CONTRAT\s+([A-Z\s]+)'
            ],
            "numero_contrat": [
                r'(?:Numéro|N°|Ref|Référence).*?(?:contrat|convention)\s*[=:]\s*([A-Z0-9\-\/]+)',
                r'Contrat\s+(?:n°|numéro)\s*[=:]\s*([A-Z0-9\-\/]+)',
                r'\b([A-Z]{2,4}[0-9]{6,})\b'
            ],
            "date_edition": [
                r'(?:Edité|Établi)\s*le\s*[=:]?\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})',
                r'Date.*?édition\s*[=:]\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})'
            ],
            "date_signature": [
                r'(?:Fait|Signé)\s+(?:à.*?)?le\s+([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})',
                r'Date.*?signature\s*[=:]\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})'
            ]
        }

        # Patterns ultra-précis pour informations client
        client_patterns = {
            "code_client": [
                r'Code\s+Client\s*[=:]?\s*([A-Z0-9]{4,12})',
                r'Client\s+(?:n°|numéro)\s*[=:]?\s*([A-Z0-9]{4,12})'
            ],
            "nom": [
                r'Nom\s+(?:du\s+)?Client\s*[=:]?\s*([A-Z]{2,25})',
                r'Nom\s*[=:]?\s*([A-Z]{2,25})(?=\s+Prénom|\s+$)'
            ],
            "prenom": [
                r'Prénom\s+(?:du\s+)?Client\s*[=:]?\s*([A-Z]{2,25})',
                r'Prénom\s*[=:]?\s*([A-Z]{2,25})'
            ],
            "numero_cin": [
                r'(?:CIN|C\.I\.N)\s*[=:]?\s*([0-9]{8})',
                r'Carte.*?Identité.*?(?:n°|numéro)\s*[=:]?\s*([0-9]{8})',
                r'Pièce.*?identité.*?([0-9]{8})'
            ],
            "date_naissance": [
                r'(?:Date.*?)?[Nn]aissance\s*[=:]?\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})',
                r'Né(?:e)?\s+le\s+([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4})'
            ],
            "telephone_mobile": [
                r'(?:Mobile|Tél\.?\s+Mobile|GSM)\s*[=:]?\s*([0-9\s\.\-\+]{8,15})',
                r'(?:\+216\s*)?([0-9]{2}\s*[0-9]{3}\s*[0-9]{3})'
            ]
        }

        # Patterns pour informations financières
        financial_patterns = {
            "montant_principal": [
                r'(?:Montant|Somme).*?(?:principal|demandé|accordé)\s*[=:]?\s*([0-9\s,\.]+)\s*(?:DT|dinars?)',
                r'Prêt\s+de\s+([0-9\s,\.]+)\s*(?:DT|dinars?)'
            ],
            "taux_interet": [
                r'Taux.*?(?:intérêt|d\'intérêt)\s*[=:]?\s*([0-9,\.]+)\s*%',
                r'([0-9,\.]+)\s*%.*?(?:an|annuel)'
            ],
            "mensualite": [
                r'(?:Mensualité|Échéance)\s*[=:]?\s*([0-9\s,\.]+)\s*(?:DT|dinars?)',
                r'Remboursement.*?mensuel\s*[=:]?\s*([0-9\s,\.]+)'
            ]
        }

        # Extraction avec validation
        all_patterns = {
            "informations_banque": bank_patterns,
            "informations_contrat": contract_patterns,
            "informations_client": client_patterns,
            "informations_financieres": financial_patterns
        }

        for section, patterns in all_patterns.items():
            for field, field_patterns in patterns.items():
                for pattern in field_patterns:
                    match = re.search(pattern, text, re.IGNORECASE | re.MULTILINE)
                    if match:
                        value = match.group(1).strip() if match.groups() else match.group(0).strip()
                        if self.validate_extracted_value(value, field):
                            result[section][field] = value
                            break

        # Détection des champs masqués
        masked_patterns = [
            (r'Code\s+Client\s+(X+)', 'code_client'),
            (r'Nom.*?Client\s+(X+)', 'nom'),
            (r'Prénom.*?Client\s+(X+)', 'prenom'),
            (r'(?:CIN|Carte.*?Identité).*?(X+)', 'numero_cin'),
            (r'(?:Date.*?)?[Nn]aissance.*?(X+)', 'date_naissance'),
            (r'(?:Mobile|Téléphone).*?(X+)', 'telephone')
        ]

        for pattern, field_name in masked_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                result["champs_masques"].append({
                    "champ": field_name,
                    "masque": match,
                    "longueur_masque": len(match)
                })

        return result

    def validate_extracted_value(self, value: str, field: str) -> bool:
        """Validation avancée des valeurs extraites"""
        if not value or len(value.strip()) < 2:
            return False

        # Validation par type de champ
        validations = {
            "numero_cin": lambda v: len(re.sub(r'[^0-9]', '', v)) == 8,
            "numero_rc": lambda v: v.startswith('B') and len(v) >= 10,
            "numero_tva": lambda v: v.isdigit() and len(v) >= 6,
            "date_naissance": lambda v: bool(re.match(r'[0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4}', v)),
            "date_edition": lambda v: bool(re.match(r'[0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4}', v)),
            "date_signature": lambda v: bool(re.match(r'[0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{4}', v)),
            "telephone_mobile": lambda v: len(re.sub(r'[^0-9]', '', v)) >= 8,
            "code_client": lambda v: len(v) >= 4 and len(v) <= 12,
            "nom": lambda v: v.isupper() and len(v) <= 25,
            "prenom": lambda v: v.isupper() and len(v) <= 25
        }

        if field in validations:
            return validations[field](value)

        return True

    def process_contract_with_llm(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement complet d'un contrat avec LLM"""
        logger.info(f"🚀 TRAITEMENT AVANCÉ LLM: {pdf_name}")

        try:
            # 1. Extraction OCR avancée
            logger.info("📄 Phase 1: Extraction OCR avancée...")
            ocr_text = self.extract_text_with_advanced_ocr(pdf_path, pdf_name)

            if not ocr_text.strip():
                raise Exception("Aucun texte extrait du PDF")

            logger.info(f"📝 Longueur du texte: {len(ocr_text)} caractères")

            # 2. Analyse avec OpenAI GPT-4
            logger.info("🧠 Phase 2: Analyse OpenAI GPT-4...")
            openai_result = self.analyze_with_openai_gpt4(ocr_text)

            # 3. Analyse avec DeepSeek
            logger.info("🧠 Phase 3: Analyse DeepSeek...")
            deepseek_result = self.analyze_with_deepseek(ocr_text)

            # 4. Extraction de secours avec regex
            logger.info("🔧 Phase 4: Extraction de secours...")
            fallback_result = self.extract_with_fallback_regex(ocr_text)

            # 5. Fusion et validation des résultats
            logger.info("🔄 Phase 5: Fusion et validation...")
            final_result = self.merge_and_validate_results(openai_result, deepseek_result, fallback_result)

            # 6. Ajout des métadonnées
            final_result['metadata'] = {
                'fichier_source': f"{pdf_name}.pdf",
                'extraction_timestamp': datetime.now().isoformat(),
                'methode_extraction': 'Advanced LLM Contract Extractor',
                'ocr_text_length': len(ocr_text),
                'modeles_utilises': {
                    'openai_gpt4': bool(openai_result),
                    'deepseek': bool(deepseek_result),
                    'regex_fallback': True
                },
                'qualite_extraction': self.evaluate_extraction_quality(final_result)
            }

            # 7. Affichage des résultats
            self.display_extraction_results(final_result, pdf_name)

            return final_result

        except Exception as e:
            logger.error(f"❌ Erreur lors du traitement de {pdf_name}: {str(e)}")
            return {
                "erreur": str(e),
                "fichier_source": f"{pdf_name}.pdf",
                "informations_banque": {},
                "informations_contrat": {},
                "informations_client": {},
                "informations_financieres": {},
                "champs_masques": []
            }

    def evaluate_extraction_quality(self, result: Dict) -> str:
        """Évalue la qualité de l'extraction"""
        total_fields = 0
        extracted_fields = 0

        sections = ["informations_banque", "informations_contrat", "informations_client", "informations_financieres"]

        for section in sections:
            if section in result:
                section_data = result[section]
                for key, value in section_data.items():
                    if not key.endswith('_validation'):
                        total_fields += 1
                        if value and value not in ["NON_DETECTE", "MASQUÉ"]:
                            extracted_fields += 1

        if total_fields == 0:
            return "AUCUNE_DONNEE"

        extraction_rate = extracted_fields / total_fields

        if extraction_rate >= 0.9:
            return "EXCELLENTE"
        elif extraction_rate >= 0.7:
            return "BONNE"
        elif extraction_rate >= 0.5:
            return "MOYENNE"
        else:
            return "FAIBLE"

    def display_extraction_results(self, result: Dict, pdf_name: str):
        """Affichage détaillé des résultats d'extraction"""
        logger.info(f"✅ TRAITEMENT TERMINÉ: {pdf_name}")
        logger.info(f"📊 RÉSULTATS D'EXTRACTION:")

        sections_names = {
            "informations_banque": "🏦 INFORMATIONS BANQUE",
            "informations_contrat": "📋 INFORMATIONS CONTRAT",
            "informations_client": "👤 INFORMATIONS CLIENT",
            "informations_financieres": "💰 INFORMATIONS FINANCIÈRES"
        }

        for section, title in sections_names.items():
            if section in result and result[section]:
                logger.info(f"\n{title}:")
                for key, value in result[section].items():
                    if not key.endswith('_validation'):
                        status = "✅" if value and value not in ["NON_DETECTE", "MASQUÉ"] else "❌"
                        logger.info(f"   {status} {key}: {value}")

                        # Affichage des validations croisées
                        validation_key = f"{key}_validation"
                        if validation_key in result[section]:
                            validation = result[section][validation_key]
                            logger.info(f"      🔍 Validation: {validation['confiance']} (Alt: {validation['valeur_secondaire']})")

        # Affichage des champs masqués
        if result.get("champs_masques"):
            logger.info(f"\n🎭 CHAMPS MASQUÉS ({len(result['champs_masques'])}):")
            for masked in result["champs_masques"]:
                logger.info(f"   🔒 {masked['champ']}: {masked['masque']} (longueur: {masked['longueur_masque']})")

        # Affichage de la qualité
        if 'metadata' in result:
            quality = result['metadata'].get('qualite_extraction', 'INCONNUE')
            consensus = result.get('validation_croisee', {}).get('consensus_score', 0.0)
            logger.info(f"\n📈 QUALITÉ: {quality} | CONSENSUS: {consensus:.2%}")

def main():
    """Fonction principale"""
    logger.info("🎯 === EXTRACTEUR AVANCÉ AVEC LLM ===")
    logger.info("🚀 PRÉCISION PARFAITE AVEC IA")

    # Initialisation
    extractor = AdvancedLLMContractExtractor()

    # Vérification des dossiers
    if not os.path.exists(PDF_FOLDER):
        logger.error(f"❌ Dossier {PDF_FOLDER} non trouvé")
        return

    # Création des dossiers de sortie
    for folder in [IMG_FOLDER, TEXT_FOLDER, RESULT_FOLDER]:
        os.makedirs(folder, exist_ok=True)

    # Recherche des fichiers PDF
    pdf_files = [f for f in os.listdir(PDF_FOLDER)
                 if f.lower().endswith('.pdf') and not f.startswith('~')]

    if not pdf_files:
        logger.error(f"❌ Aucun fichier PDF trouvé dans {PDF_FOLDER}")
        return

    logger.info(f"📁 Trouvé {len(pdf_files)} fichier(s) PDF à traiter")

    # Traitement de chaque fichier
    for i, pdf_file in enumerate(pdf_files, 1):
        logger.info(f"\n{'='*80}")
        logger.info(f"📋 FICHIER {i}/{len(pdf_files)}: {pdf_file}")
        logger.info(f"{'='*80}")

        pdf_path = os.path.join(PDF_FOLDER, pdf_file)
        pdf_name = pdf_file.rsplit(".", 1)[0]

        # Traitement avec LLM
        result = extractor.process_contract_with_llm(pdf_path, pdf_name)

        # Sauvegarde
        output_path = f"{RESULT_FOLDER}/{pdf_name}_LLM_ADVANCED.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

        logger.info(f"💾 Résultat sauvegardé: {output_path}")

    logger.info(f"\n{'='*80}")
    logger.info("🏁 EXTRACTION AVANCÉE TERMINÉE!")
    logger.info(f"{'='*80}")

if __name__ == "__main__":
    main()
