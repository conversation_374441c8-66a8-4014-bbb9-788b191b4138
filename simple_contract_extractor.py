#!/usr/bin/env python3
"""
EXTRACTEUR DE CONTRATS SIMPLE ET EFFICACE
Utilise OCR basique + analyse intelligente pour extraire les informations réelles
"""

import os
import json
import pytesseract
from pathlib import Path
from PIL import Image
from pdf2image import convert_from_path
import re
import cv2
import numpy as np
from datetime import datetime
import logging
import requests
from typing import Dict, List, Any, Optional

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration OCR
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'

# Dossiers
PDF_FOLDER = 'contrat'
IMG_FOLDER = 'images'
TEXT_FOLDER = 'ocr_texts'
RESULT_FOLDER = 'results'

class SimpleContractExtractor:
    """Extracteur simple et efficace pour contrats"""
    
    def __init__(self):
        # Configuration OCR basique
        self.ocr_config = r'--oem 3 --psm 6'
    
    def extract_text_from_pdf(self, pdf_path: str, pdf_name: str) -> str:
        """Extrait le texte d'un PDF avec OCR basique"""
        logger.info(f"📄 Extraction OCR basique pour {pdf_name}")
        
        # Créer les dossiers
        os.makedirs(IMG_FOLDER, exist_ok=True)
        os.makedirs(TEXT_FOLDER, exist_ok=True)
        
        # Conversion PDF en images
        pages = convert_from_path(pdf_path, dpi=300, fmt='PNG')
        
        all_text = ""
        
        for i, page in enumerate(pages):
            page_num = i + 1
            logger.info(f"🔍 Traitement page {page_num}...")
            
            # Amélioration basique de l'image
            enhanced_page = self.enhance_image_basic(page)
            
            # Sauvegarde de l'image
            img_path = f"{IMG_FOLDER}/{pdf_name}_page_{page_num}.png"
            enhanced_page.save(img_path, "PNG")
            
            # OCR simple
            text = pytesseract.image_to_string(enhanced_page, lang='fra+eng', config=self.ocr_config)
            
            # Sauvegarde du texte
            text_path = f"{TEXT_FOLDER}/{pdf_name}_page_{page_num}.txt"
            with open(text_path, 'w', encoding='utf-8') as f:
                f.write(text)
            
            all_text += f"\n--- Page {page_num} ---\n{text}"
            
            logger.info(f"✅ Page {page_num} traitée")
        
        # Nettoyage basique du texte
        cleaned_text = self.clean_text_basic(all_text)
        
        return cleaned_text
    
    def enhance_image_basic(self, image: Image.Image) -> Image.Image:
        """Amélioration basique de l'image pour OCR"""
        if image.mode != 'L':
            image = image.convert('L')
        
        img_array = np.array(image)
        
        # Débruitage simple
        denoised = cv2.fastNlMeansDenoising(img_array, None, 10, 7, 21)
        
        # Amélioration du contraste
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(denoised)
        
        # Binarisation simple
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        return Image.fromarray(binary)
    
    def clean_text_basic(self, text: str) -> str:
        """Nettoyage basique du texte OCR"""
        if not text:
            return ""
        
        # Corrections basiques
        text = re.sub(r'\s+', ' ', text)  # Espaces multiples
        text = re.sub(r'[^\x00-\x7F]+', '', text)  # Caractères non-ASCII
        
        return text.strip()
    
    def analyze_text_with_llm(self, text: str) -> Dict:
        """Analyse le texte avec un modèle LLM (simulé ici)"""
        logger.info("🧠 Analyse intelligente du texte...")
        
        # Ici, on simulerait l'appel à un LLM
        # Dans un cas réel, on utiliserait OpenAI, HuggingFace, etc.
        
        # Extraction basique des informations
        extracted_info = self.extract_basic_info(text)
        
        return extracted_info
    
    def extract_basic_info(self, text: str) -> Dict:
        """Extraction basique des informations sans LLM"""
        logger.info("📋 Extraction des informations de base...")

    def validate_field_value(self, value: str, field: str, section: str) -> bool:
        """Valide la valeur extraite pour éviter les fausses détections"""
        if not value or len(value) < 2:
            return False

        # Validation par longueur maximale
        max_lengths = {
            "nom": 30, "prenom": 30, "code_client": 15, "date_naissance": 15,
            "profession": 30, "adresse": 100, "telephone": 20, "nationalite": 20,
            "situation_familiale": 30
        }

        if field in max_lengths and len(value) > max_lengths[field]:
            return False

        # Validation par contenu
        if 'X' in value and section != "client":
            return False  # Les X sont uniquement pour les champs client masqués

        # Validation spécifique par type de champ
        if field == "code_client" and len(value) > 10:
            return False

        if field in ["nom", "prenom"] and len(value) > 30:
            return False

        if field == "date_naissance" and not (re.match(r'[0-9X/]{1,10}', value) or 'X' in value):
            return False

        if field in ["telephone", "telephone_mobile", "fax"] and not re.match(r'[0-9\s\.\-\+\(\)]{3,20}', value):
            return False

        if field == "adresse" and len(value) > 100:
            return False

        # Validation par mots-clés interdits
        forbidden_keywords = ["Informations", "Situation du Client", "Emploi Budget",
                             "Adresse Permanente", "Adresse Courrier", "Type", "Segment"]

        for keyword in forbidden_keywords:
            if keyword in value:
                return False

        return True

    def extract_basic_info(self, text: str) -> Dict:
        """Extraction basique des informations sans LLM"""
        logger.info("📋 Extraction des informations de base...")
        
        # Structure simple pour les résultats
        result = {
            "informations_banque": {},
            "informations_contrat": {},
            "informations_client": {},
            "champs_masques": []
        }
        
        # Extraction des informations de la banque (patterns améliorés)
        bank_patterns = {
            "nom_banque": [r'BANQUE\s+TUNISO[\-\s]?KOWEITIENNE', r'BTK', r'Banque\s+Tuniso[\-\s]?Koweitienne'],
            "capital_social": [r'capital\s+de\s+([0-9\s]+)\s+dinars?', r'au\s+capital\s+de\s+([0-9\s]+)', r'([0-9]{3}\s+[0-9]{3}\s+[0-9]{3})\s+dinars?'],
            "numero_rc": [r'R\.?C\.?\s*[=:]\s*(B[0-9]+)', r'RC\s*[=:]\s*(B[0-9]+)', r'\b(B[0-9]{9,12})\b'],
            "numero_tva": [r'TVA\s*[=:]\s*([0-9]+)', r'T\.V\.A\s*[=:]\s*([0-9]+)'],
            "adresse": [r'([0-9]+\s+bis,?\s+Av\.?\s+Mohamed\s+V[^|]{10,50})', r'(Avenue\s+Mohamed\s+V.*?Tunis)', r'([0-9]+\s+bis.*?Tunis)'],
            "telephone": [r'Tél\s*:?\s*\(\+216\)\s*([0-9\s\.]+)', r'\(\+216\)\s*([0-9\s\.]+)'],
            "fax": [r'Fax\s*:?\s*\(\+216\)\s*([0-9\s\.]+)'],
            "site_web": [r'(www\.btknet\.com)', r'Site.*?(www\.btknet\.com)', r'(www\.biknet\.com)'],
            "forme_juridique": [r'société\s+anonyme', r'S\.A\.', r'SARL']
        }
        
        # Extraction des informations du contrat (patterns améliorés)
        contract_patterns = {
            "type_contrat": [r'CONTRAT\s+DE\s+(PRET|PRÊT)', r'Convention\s+de\s+([A-Za-z\s]+)', r'Informations\s+du\s+(Client)', r'CONTRAT\s+([A-Z\s]+)'],
            "numero_contrat": [r'Numéro.*?contrat\s*[=:]\s*([A-Z0-9\-]+)', r'Référence\s*[=:]\s*([A-Z0-9\-]+)', r'N°.*?([A-Z0-9]{6,})'],
            "date_edition": [r'Edité\s*le\s*:?\s*([0-9]{1,2}/[0-9]{1,2}/[0-9]{4})', r'Le\s*,?\s*([0-9]{1,2}/[0-9]{1,2}/[0-9]{4})'],
            "date_signature": [r'Fait\s+à.*?le\s+([0-9]{1,2}/[0-9]{1,2}/[0-9]{4})', r'Signé\s+le\s+([0-9]{1,2}/[0-9]{1,2}/[0-9]{4})'],
            "montant_principal": [r'somme\s+principale\s+([0-9\s,\.]+)', r'montant\s+total.*?([0-9\s,\.]+)', r'prêt\s+de\s+([0-9\s,\.]+)'],
            "duree": [r'durée\s+de\s+([0-9]+\s+mois)', r'consenti\s+pour.*?([0-9]+\s+mois)', r'période\s+de\s+([0-9]+\s+[a-z]+)'],
            "taux_interet": [r'taux\s+semestriel\s+([^,]+)', r'intérêts\s+au\s+taux\s+([^,]+)', r'taux.*?([0-9,\.]+\s*%)'],
            "commission": [r'commission\s+de\s+gestion\s+de\s+([0-9,\.]+\s*%)', r'Commission.*?([0-9,\.]+\s*%)'],
            "garanties": [r'garanties?\s+([^.]+)', r'caution.*?([^.]+)']
        }
        
        # Extraction des informations du client (patterns CORRIGÉS)
        client_patterns = {
            "code_client": [r'Code\s+Client\s+([A-Z0-9X]{2,10})(?=\s|$)', r'Client\s+([A-Z0-9]{6,})'],
            "nom": [r'Nom\s+du\s+Client\s+([A-Z]{2,20})(?=\s|$)', r'Nom\s*:\s*([A-Z]{2,20})(?=\s|$)'],
            "prenom": [r'Prénom\s+du\s+Client\s+([A-Z]{2,20})(?=\s|$)', r'Prénom\s*:\s*([A-Z]{2,20})(?=\s|$)'],
            "date_naissance": [r'Date\s+de\s+Naissance\s+([0-9X]{1,10})(?=\s|$)', r'Naissance\s*:\s*([0-9X/]{1,10})(?=\s|$)'],
            "nationalite": [r'Nationalité\s+([A-Z]{2,15})(?=\s|$)', r'Nat\.\s+([A-Z]{2,15})(?=\s|$)'],
            "situation_familiale": [r'Situation\s+Familiale\s+([A-Za-z]{2,15})(?=\s|$)', r'Rég\.\s+Matrimonial\s+([A-Za-z]{2,15})(?=\s|$)'],
            "profession": [r'Profession\s+([A-ZX]{2,20})(?=\s|$)', r'Emploi\s+([A-ZX]{2,20})(?=\s|$)'],
            "telephone_mobile": [r'Mobile\s+([0-9X]{3,15})(?=\s|$)', r'Téléphone\s+([0-9X]{3,15})(?=\s|$)'],
            "segment": [r'(PARTICULIER\s+RESIDENT)', r'(PROFESSIONNEL)', r'(ENTREPRISE)'],
            "secteur_activite": [r'Secteur\s+d\'activité\s+(PARTICULIERS)', r'(PARTICULIERS)\s*$']
        }
        
        # Détection des champs masqués (patterns améliorés)
        masked_patterns = [
            (r'Code\s+Client\s+(X+)', 'code_client'),
            (r'Nom.*?Client\s+(X+)', 'nom_client'),
            (r'Prénom.*?Client\s+(X+)', 'prenom_client'),
            (r'Date.*?Naissance\s+(X+)', 'date_naissance'),
            (r'Nationalité\s+(X+)', 'nationalite'),
            (r'Situation\s+Familiale\s+(X+)', 'situation_familiale'),
            (r'Adresse.*?(X{3,})', 'adresse'),
            (r'Mobile\s+(X+)', 'telephone_mobile'),
            (r'Profession\s+(X+)', 'profession'),
            (r'Type\s+(X+)', 'type_piece_identite'),
            (r'Numéro\s+(X+)', 'numero_piece_identite')
        ]
        
        # Extraction pour chaque section avec validation
        for field, patterns in bank_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    if match.groups():
                        value = match.group(1).strip()
                    else:
                        value = match.group(0).strip()

                    # Validation de la valeur
                    if self.validate_field_value(value, field, "banque"):
                        result["informations_banque"][field] = value
                    break
        
        for field, patterns in contract_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    if match.groups():
                        value = match.group(1).strip()
                    else:
                        value = match.group(0).strip()

                    # Validation de la valeur
                    if self.validate_field_value(value, field, "contrat"):
                        result["informations_contrat"][field] = value
                    break
        
        for field, patterns in client_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    if match.groups():
                        value = match.group(1).strip()
                    else:
                        value = match.group(0).strip()

                    # Validation de la valeur
                    if self.validate_field_value(value, field, "client"):
                        result["informations_client"][field] = value
                    break
        
        # Détection des champs masqués
        for pattern, field_name in masked_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                result["champs_masques"].append({
                    "champ": field_name,
                    "masque": match,
                    "longueur_masque": len(match)
                })
        
        return result
    
    def process_contract(self, pdf_path: str, pdf_name: str) -> Dict:
        """Traitement complet d'un contrat"""
        logger.info(f"🚀 TRAITEMENT: {pdf_name}")
        
        try:
            # 1. Extraction OCR basique
            logger.info("📄 Phase 1: Extraction OCR basique...")
            ocr_text = self.extract_text_from_pdf(pdf_path, pdf_name)
            
            if not ocr_text.strip():
                raise Exception("Aucun texte extrait du PDF")
            
            logger.info(f"📝 Longueur du texte: {len(ocr_text)} caractères")
            
            # 2. Analyse du texte
            logger.info("🧠 Phase 2: Analyse du texte...")
            extracted_info = self.analyze_text_with_llm(ocr_text)
            
            # 3. Ajout des métadonnées
            extracted_info['metadata'] = {
                'fichier_source': f"{pdf_name}.pdf",
                'extraction_timestamp': datetime.now().isoformat(),
                'methode_extraction': 'Simple Contract Extractor',
                'ocr_text_length': len(ocr_text)
            }
            
            # 4. Affichage des résultats
            logger.info(f"✅ TRAITEMENT TERMINÉ!")
            logger.info(f"📋 Informations extraites:")
            
            for section, data in extracted_info.items():
                if section != 'metadata' and section != 'champs_masques':
                    fields_count = len(data)
                    logger.info(f"   📂 {section}: {fields_count} champs")
            
            logger.info(f"   🎭 Champs masqués: {len(extracted_info['champs_masques'])}")
            
            return extracted_info
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du traitement de {pdf_name}: {str(e)}")
            return {
                "erreur": str(e),
                "fichier_source": f"{pdf_name}.pdf",
                "informations_banque": {},
                "informations_contrat": {},
                "informations_client": {},
                "champs_masques": []
            }

def main():
    """Fonction principale"""
    logger.info("🎯 === EXTRACTEUR DE CONTRATS SIMPLE ET EFFICACE ===")
    logger.info("🚀 EXTRACTION DES INFORMATIONS RÉELLES")
    
    # Initialisation
    extractor = SimpleContractExtractor()
    
    # Vérification des dossiers
    if not os.path.exists(PDF_FOLDER):
        logger.error(f"❌ Dossier {PDF_FOLDER} non trouvé")
        return
    
    # Création des dossiers de sortie
    for folder in [IMG_FOLDER, TEXT_FOLDER, RESULT_FOLDER]:
        os.makedirs(folder, exist_ok=True)
    
    # Recherche des fichiers PDF
    pdf_files = [f for f in os.listdir(PDF_FOLDER) 
                 if f.lower().endswith('.pdf') and not f.startswith('~')]
    
    if not pdf_files:
        logger.error(f"❌ Aucun fichier PDF trouvé dans {PDF_FOLDER}")
        return
    
    logger.info(f"📁 Trouvé {len(pdf_files)} fichier(s) PDF à traiter")
    
    # Traitement de chaque fichier
    for i, pdf_file in enumerate(pdf_files, 1):
        logger.info(f"\n{'='*80}")
        logger.info(f"📋 FICHIER {i}/{len(pdf_files)}: {pdf_file}")
        logger.info(f"{'='*80}")
        
        pdf_path = os.path.join(PDF_FOLDER, pdf_file)
        pdf_name = pdf_file.rsplit(".", 1)[0]
        
        # Traitement
        result = extractor.process_contract(pdf_path, pdf_name)
        
        # Sauvegarde
        output_path = f"{RESULT_FOLDER}/{pdf_name}_SIMPLE.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Résultat sauvegardé: {output_path}")
    
    logger.info(f"\n{'='*80}")
    logger.info("🏁 EXTRACTION TERMINÉE!")
    logger.info(f"{'='*80}")

if __name__ == "__main__":
    main()


