#!/usr/bin/env python3
"""
Script de test pour l'extracteur LLM
"""

import os
from advanced_contract_extractor_llm import AdvancedLLMContractExtractor
from config import validate_config

def test_configuration():
    """Test de la configuration"""
    print("🧪 TEST DE CONFIGURATION")
    print("=" * 50)
    
    # Validation de la config
    if not validate_config():
        print("❌ Configuration invalide")
        return False
    
    # Test de l'extracteur
    try:
        extractor = AdvancedLLMContractExtractor()
        print("✅ Extracteur initialisé")
        return True
    except Exception as e:
        print(f"❌ Erreur initialisation: {e}")
        return False

def test_pdf_processing():
    """Test du traitement PDF"""
    print("\n🧪 TEST TRAITEMENT PDF")
    print("=" * 50)
    
    pdf_folder = 'contrat'
    if not os.path.exists(pdf_folder):
        print(f"❌ Dossier {pdf_folder} non trouvé")
        return False
    
    pdf_files = [f for f in os.listdir(pdf_folder) if f.endswith('.pdf')]
    if not pdf_files:
        print(f"❌ Aucun PDF dans {pdf_folder}")
        return False
    
    print(f"✅ {len(pdf_files)} PDF(s) trouvé(s)")
    
    # Test sur le premier PDF
    test_pdf = pdf_files[0]
    print(f"🔍 Test sur: {test_pdf}")
    
    try:
        extractor = AdvancedLLMContractExtractor()
        pdf_path = os.path.join(pdf_folder, test_pdf)
        pdf_name = test_pdf.rsplit('.', 1)[0]
        
        # Test extraction OCR seulement
        text = extractor.extract_text_with_advanced_ocr(pdf_path, pdf_name)
        if text:
            print(f"✅ OCR réussi: {len(text)} caractères")
            return True
        else:
            print("❌ OCR échoué")
            return False
            
    except Exception as e:
        print(f"❌ Erreur test: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TESTS EXTRACTEUR LLM")
    print("=" * 60)
    
    success = True
    
    # Test configuration
    if not test_configuration():
        success = False
    
    # Test traitement PDF
    if not test_pdf_processing():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TOUS LES TESTS RÉUSSIS!")
        print("✅ Vous pouvez utiliser l'extracteur LLM")
    else:
        print("❌ CERTAINS TESTS ONT ÉCHOUÉ")
        print("🔧 Vérifiez la configuration")
